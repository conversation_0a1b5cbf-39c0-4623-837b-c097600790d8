#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini-Balance 一键启动器
为 <PERSON><PERSON><PERSON>-King 项目提供 Gemini-Balance 服务的一键启动和管理功能
"""

import os
import sys
import subprocess
import time
import json
import requests
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class GeminiBalanceLauncher:
    """Gemini-Balance 启动器"""
    
    def __init__(self, base_dir: str = None):
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent.parent.parent
        self.gemini_balance_dir = self.base_dir / "gemini-balance"
        self.process = None
        self.status = "stopped"
        self.port = 8000
        self.host = "0.0.0.0"
        
    def check_environment(self) -> Dict[str, bool]:
        """检查运行环境"""
        checks = {}
        
        # 检查Python版本
        python_version = sys.version_info
        checks["python_version"] = python_version >= (3, 8)
        
        # 检查gemini-balance目录
        checks["gemini_balance_exists"] = self.gemini_balance_dir.exists()
        
        # 检查requirements.txt
        requirements_file = self.gemini_balance_dir / "requirements.txt"
        checks["requirements_exists"] = requirements_file.exists()
        
        # 检查.env文件
        env_file = self.gemini_balance_dir / ".env"
        checks["env_file_exists"] = env_file.exists()
        
        # 检查端口是否可用
        checks["port_available"] = self._check_port_available(self.port)
        
        return checks
    
    def _check_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False
    
    def install_dependencies(self) -> bool:
        """安装依赖"""
        try:
            requirements_file = self.gemini_balance_dir / "requirements.txt"
            if not requirements_file.exists():
                print("❌ requirements.txt 文件不存在")
                return False
            
            print("📦 正在安装依赖...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], capture_output=True, text=True, cwd=str(self.gemini_balance_dir))
            
            if result.returncode == 0:
                print("✅ 依赖安装成功")
                return True
            else:
                print(f"❌ 依赖安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 安装依赖时出错: {e}")
            return False
    
    def setup_config(self, api_keys: List[str] = None, database_type: str = "sqlite") -> bool:
        """设置配置文件"""
        try:
            env_file = self.gemini_balance_dir / ".env"
            
            # 默认配置
            config = {
                "DATABASE_TYPE": database_type,
                "SQLITE_DATABASE": str(self.gemini_balance_dir / "data" / "gemini_balance.db"),
                "API_KEYS": json.dumps(api_keys or ["AIzaSyDummy-Key-For-Testing-Replace-With-Real-Key"]),
                "ALLOWED_TOKENS": '["sk-123456"]',
                "AUTH_TOKEN": "sk-123456",
                "TEST_MODEL": "gemini-1.5-flash",
                "BASE_URL": "https://generativelanguage.googleapis.com/v1beta",
                "MAX_FAILURES": "3",
                "MAX_RETRIES": "3",
                "CHECK_INTERVAL_HOURS": "1",
                "TIMEZONE": "Asia/Shanghai",
                "TIME_OUT": "300",
                "LOG_LEVEL": "INFO",
                "SAFETY_SETTINGS": '[{"category": "HARM_CATEGORY_HARASSMENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "OFF"}, {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "OFF"}]',
                "IMAGE_MODELS": '["gemini-2.0-flash-exp"]',
                "SEARCH_MODELS": '["gemini-2.0-flash-exp"]',
                "FILTERED_MODELS": "[]",
                "TOOLS_CODE_EXECUTION_ENABLED": "false",
                "SHOW_SEARCH_LINK": "true",
                "SHOW_THINKING_PROCESS": "true",
                "THINKING_MODELS": "[]",
                "THINKING_BUDGET_MAP": "{}",
                "URL_NORMALIZATION_ENABLED": "false",
                "URL_CONTEXT_ENABLED": "false",
                "URL_CONTEXT_MODELS": "[]",
                "CREATE_IMAGE_MODEL": "imagen-3.0-generate-002",
                "UPLOAD_PROVIDER": "smms",
                "SMMS_SECRET_TOKEN": "your-smms-token",
                "TTS_MODEL": "gemini-2.5-flash-preview-tts",
                "TTS_VOICE_NAME": "Zephyr",
                "TTS_SPEED": "normal",
                "STREAM_OPTIMIZER_ENABLED": "false",
                "STREAM_MIN_DELAY": "0.016",
                "STREAM_MAX_DELAY": "0.024",
                "STREAM_SHORT_TEXT_THRESHOLD": "10",
                "STREAM_LONG_TEXT_THRESHOLD": "50",
                "STREAM_CHUNK_SIZE": "5",
                "FAKE_STREAM_ENABLED": "false",
                "FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS": "5",
                "AUTO_DELETE_ERROR_LOGS_ENABLED": "true",
                "AUTO_DELETE_ERROR_LOGS_DAYS": "7",
                "AUTO_DELETE_REQUEST_LOGS_ENABLED": "false",
                "AUTO_DELETE_REQUEST_LOGS_DAYS": "30",
                "PROXIES": "[]"
            }
            
            # 创建data目录
            data_dir = self.gemini_balance_dir / "data"
            data_dir.mkdir(exist_ok=True)
            
            # 写入配置文件
            with open(env_file, 'w', encoding='utf-8') as f:
                for key, value in config.items():
                    f.write(f"{key}={value}\n")
            
            print("✅ 配置文件创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    
    def start_service(self) -> bool:
        """启动服务"""
        try:
            if self.status == "running":
                print("⚠️ 服务已在运行中")
                return True
            
            print("🚀 正在启动 Gemini-Balance 服务...")
            
            # 启动命令
            cmd = [
                sys.executable, "-m", "uvicorn", "app.main:app",
                "--host", self.host,
                "--port", str(self.port),
                "--reload"
            ]
            
            # 启动进程
            self.process = subprocess.Popen(
                cmd,
                cwd=str(self.gemini_balance_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务启动
            if self._wait_for_service():
                self.status = "running"
                print(f"✅ Gemini-Balance 服务启动成功!")
                print(f"🌐 访问地址: http://localhost:{self.port}")
                print(f"📚 API文档: http://localhost:{self.port}/docs")
                print(f"⚙️ 配置页面: http://localhost:{self.port}/config")
                return True
            else:
                self.status = "failed"
                print("❌ 服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动服务时出错: {e}")
            self.status = "failed"
            return False
    
    def _wait_for_service(self, timeout: int = 30) -> bool:
        """等待服务启动"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"http://localhost:{self.port}/", timeout=2)
                if response.status_code == 200:
                    return True
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)
        return False
    
    def stop_service(self) -> bool:
        """停止服务"""
        try:
            if self.process and self.process.poll() is None:
                self.process.terminate()
                self.process.wait(timeout=10)
                print("✅ 服务已停止")
            
            self.status = "stopped"
            self.process = None
            return True
            
        except Exception as e:
            print(f"❌ 停止服务时出错: {e}")
            return False
    
    def get_status(self) -> Dict:
        """获取服务状态"""
        if self.process and self.process.poll() is None:
            self.status = "running"
        else:
            self.status = "stopped"
        
        return {
            "status": self.status,
            "port": self.port,
            "host": self.host,
            "url": f"http://localhost:{self.port}",
            "pid": self.process.pid if self.process else None
        }
    
    def one_click_start(self, api_keys: List[str] = None) -> bool:
        """一键启动"""
        print("🎯 开始一键启动 Gemini-Balance...")
        
        # 1. 环境检查
        print("\n1️⃣ 检查环境...")
        checks = self.check_environment()
        
        for check, result in checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check}: {result}")
        
        if not all(checks.values()):
            if not checks["gemini_balance_exists"]:
                print("❌ gemini-balance 目录不存在，请先克隆项目")
                return False
            
            if not checks["port_available"]:
                print(f"❌ 端口 {self.port} 被占用，请检查或更换端口")
                return False
        
        # 2. 安装依赖
        print("\n2️⃣ 安装依赖...")
        if not self.install_dependencies():
            return False
        
        # 3. 设置配置
        print("\n3️⃣ 设置配置...")
        if not self.setup_config(api_keys):
            return False
        
        # 4. 启动服务
        print("\n4️⃣ 启动服务...")
        if not self.start_service():
            return False
        
        print("\n🎉 一键启动完成!")
        return True


def main():
    """主函数"""
    launcher = GeminiBalanceLauncher()
    
    print("🚀 Hajimi-King Gemini-Balance 一键启动器")
    print("=" * 50)
    
    # 一键启动
    success = launcher.one_click_start()
    
    if success:
        print("\n✨ 启动成功! 按 Ctrl+C 停止服务")
        try:
            # 保持运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            launcher.stop_service()
            print("👋 再见!")
    else:
        print("\n❌ 启动失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
