# Gemini Balance 启动说明

## 🎉 项目已成功克隆并启动！

### 当前状态
- ✅ 项目已克隆到：`d:\aa\gemini-balance`
- ✅ 依赖已安装完成
- ✅ 应用已启动，运行在：http://localhost:8000
- ⚠️ 数据库配置需要完善（目前有SQLite路径问题）

### 访问地址
- **主页面**：http://localhost:8000
- **API文档**：http://localhost:8000/docs
- **Key状态监控**：http://localhost:8000/keys_status（需要认证）

### 下一步配置

#### 1. 配置Gemini API密钥
编辑 `.env` 文件，将以下配置替换为您的实际API密钥：
```env
API_KEYS=["your-actual-gemini-api-key-here"]
```

#### 2. 解决数据库问题
当前SQLite数据库路径有问题，建议：

**选项A：使用绝对路径**
```env
SQLITE_DATABASE=D:/aa/gemini-balance/data/gemini_balance.db
```

**选项B：使用MySQL（推荐生产环境）**
```env
DATABASE_TYPE=mysql
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=gemini_balance
```

#### 3. 配置访问令牌
```env
ALLOWED_TOKENS=["sk-your-custom-token"]
AUTH_TOKEN=sk-your-custom-token
```

### 重启应用
修改配置后，在终端中按 `Ctrl+C` 停止应用，然后重新运行：
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 功能特性
- 🔄 多API密钥负载均衡
- 🌐 支持OpenAI和Gemini API格式
- 📊 实时监控和状态页面
- 🖼️ 图像生成和处理
- 🔍 联网搜索功能
- 📝 详细日志记录

### 使用示例

#### OpenAI格式API调用
```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Authorization: Bearer sk-123456" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

#### Gemini格式API调用
```bash
curl -X POST "http://localhost:8000/gemini/v1beta/models/gemini-1.5-flash:generateContent" \
  -H "Authorization: Bearer sk-123456" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [{"parts": [{"text": "Hello!"}]}]
  }'
```

### 故障排除
1. **数据库错误**：确保data目录存在且有写入权限
2. **API密钥错误**：检查.env文件中的API_KEYS配置
3. **端口占用**：如果8000端口被占用，可以修改启动命令中的端口号

### 更多信息
- 查看 `README_ZH.md` 了解完整功能说明
- 访问 http://localhost:8000/docs 查看API文档
- 检查日志文件了解详细错误信息
