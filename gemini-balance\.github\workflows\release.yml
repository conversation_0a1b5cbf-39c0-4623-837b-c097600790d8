name: Publish Release

on:
  push:
    tags:
      - "v*" # 当推送以 "v" 开头的标签时触发（如 v1.0.0, v2.1.0）

jobs:
  update-release-draft:
    permissions:
      contents: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      # Step 1: 检出代码库
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      # Step 2: 自动生成 Release Notes
      - name: Generate release notes
        id: changelog
        uses: mikepenz/release-changelog-builder-action@v4
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Step 3: 自动生成 Release
      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}
          release_name: ${{ github.ref_name }}
          body: ${{ steps.changelog.outputs.changelog }}
          draft: false
          prerelease: false

      # Step 4: 可选，构建zip文件
      - name: Create ZIP file
        run: |
          zip -r gemini-balance.zip . -x "*.git*" "*.github*" "*.env*" "logs/*" "tests/*"

      # Step 5: 可选，上传构建文件
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./gemini-balance.zip # 替换为你的构建文件路径
          asset_name: gemini-balance.zip # 替换为你的文件名
          asset_content_type: application/zip
